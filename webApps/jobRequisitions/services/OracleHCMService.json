{"id": "OracleHCMService", "description": "Oracle HCM Recruiting Cloud REST API Service", "type": "REST", "baseURL": "{{$application.variables.oracleHCMBaseURL}}", "authentication": {"type": "bearer", "token": "{{$application.variables.authToken}}"}, "endpoints": {"getJobRequisitions": {"method": "GET", "path": "/recruitingJobRequisitions", "description": "Get all job requisitions with pagination and filtering", "parameters": {"limit": {"type": "integer", "description": "Number of records to return", "default": 25}, "offset": {"type": "integer", "description": "Starting position for pagination", "default": 0}, "q": {"type": "string", "description": "Query filter expression"}, "orderBy": {"type": "string", "description": "Sort order specification"}, "totalResults": {"type": "boolean", "description": "Include total count in response", "default": true}}, "responseType": "object"}, "getJobRequisition": {"method": "GET", "path": "/recruitingJobRequisitions/{id}", "description": "Get a specific job requisition by ID", "parameters": {"id": {"type": "integer", "required": true, "description": "Requisition ID"}}, "responseType": "JobRequisition"}, "createJobRequisition": {"method": "POST", "path": "/recruitingJobRequisitions", "description": "Create a new job requisition", "requestType": "JobRequisition", "responseType": "JobRequisition"}, "updateJobRequisition": {"method": "PATCH", "path": "/recruitingJobRequisitions/{id}", "description": "Update an existing job requisition", "parameters": {"id": {"type": "integer", "required": true, "description": "Requisition ID"}}, "requestType": "JobRequisition", "responseType": "JobRequisition"}, "deleteJobRequisition": {"method": "DELETE", "path": "/recruitingJobRequisitions/{id}", "description": "Delete a job requisition", "parameters": {"id": {"type": "integer", "required": true, "description": "Requisition ID"}}}}, "headers": {"Content-Type": "application/json", "Accept": "application/json", "Authorization": "{{$application.variables.authToken}}", "X-Requested-With": "XMLHttpRequest", "Cache-Control": "no-cache"}, "timeout": 30000, "retryPolicy": {"maxRetries": 3, "retryDelay": 1000}, "errorHandling": {"401": "Authentication failed - please check your credentials", "403": "Access denied - insufficient permissions", "404": "Resource not found", "500": "Internal server error - please try again later"}}