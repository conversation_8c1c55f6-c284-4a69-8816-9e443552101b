/**
 * Oracle VBCS Job Requisitions Application
 * Utility functions and helpers
 */

define([], function() {
  'use strict';

  var AppUtils = {
    
    /**
     * Format date for display
     */
    formatDate: function(dateString) {
      if (!dateString) return '';
      
      try {
        var date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        });
      } catch (e) {
        return dateString;
      }
    },
    
    /**
     * Format currency for display
     */
    formatCurrency: function(amount, currency) {
      if (!amount && amount !== 0) return '';
      
      currency = currency || 'USD';
      
      try {
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: currency
        }).format(amount);
      } catch (e) {
        return amount + ' ' + currency;
      }
    },
    
    /**
     * Get status badge class based on state
     */
    getStatusBadgeClass: function(stateName) {
      if (!stateName) return 'oj-badge-secondary';
      
      var state = stateName.toLowerCase();
      
      if (state.includes('progress') || state.includes('active')) {
        return 'oj-badge-success';
      } else if (state.includes('expired') || state.includes('closed')) {
        return 'oj-badge-danger';
      } else if (state.includes('pending') || state.includes('draft')) {
        return 'oj-badge-warning';
      } else {
        return 'oj-badge-info';
      }
    },
    
    /**
     * Validate requisition data
     */
    validateRequisition: function(requisition) {
      var errors = {};
      
      if (!requisition.RequisitionNumber || requisition.RequisitionNumber.trim() === '') {
        errors.RequisitionNumber = 'Requisition number is required';
      }
      
      if (!requisition.Title || requisition.Title.trim() === '') {
        errors.Title = 'Job title is required';
      }
      
      if (!requisition.RecruitingType) {
        errors.RecruitingType = 'Recruiting type is required';
      }
      
      if (!requisition.NumberOfOpenings || requisition.NumberOfOpenings < 1) {
        errors.NumberOfOpenings = 'Number of openings must be at least 1';
      }
      
      if (requisition.MinimumSalary && requisition.MaximumSalary && 
          requisition.MinimumSalary > requisition.MaximumSalary) {
        errors.MaximumSalary = 'Maximum salary must be greater than minimum salary';
      }
      
      return {
        isValid: Object.keys(errors).length === 0,
        errors: errors
      };
    },
    
    /**
     * Build query string for API calls
     */
    buildQueryString: function(searchQuery, filters) {
      var queryParts = [];
      
      if (searchQuery && searchQuery.trim() !== '') {
        queryParts.push("Title like '%" + searchQuery.trim() + "%'");
      }
      
      if (filters) {
        if (filters.recruitingType) {
          queryParts.push("RecruitingType = '" + filters.recruitingType + "'");
        }
        
        if (filters.stateName) {
          queryParts.push("StateName = '" + filters.stateName + "'");
        }
        
        if (filters.businessUnitId) {
          queryParts.push("BusinessUnitId = " + filters.businessUnitId);
        }
      }
      
      return queryParts.join(' and ');
    },
    
    /**
     * Handle API errors
     */
    handleApiError: function(error, context) {
      console.error('API Error in ' + context + ':', error);
      
      var message = 'An error occurred';
      
      if (error && error.response) {
        if (error.response.status === 401) {
          message = 'Authentication failed. Please check your credentials.';
        } else if (error.response.status === 403) {
          message = 'Access denied. You do not have permission to perform this action.';
        } else if (error.response.status === 404) {
          message = 'Resource not found.';
        } else if (error.response.status >= 500) {
          message = 'Server error. Please try again later.';
        } else if (error.response.data && error.response.data.message) {
          message = error.response.data.message;
        }
      } else if (error && error.message) {
        message = error.message;
      }
      
      return message;
    },
    
    /**
     * Debounce function for search
     */
    debounce: function(func, wait) {
      var timeout;
      return function executedFunction() {
        var context = this;
        var args = arguments;
        
        var later = function() {
          timeout = null;
          func.apply(context, args);
        };
        
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    },
    
    /**
     * Deep clone object
     */
    deepClone: function(obj) {
      if (obj === null || typeof obj !== 'object') {
        return obj;
      }
      
      if (obj instanceof Date) {
        return new Date(obj.getTime());
      }
      
      if (obj instanceof Array) {
        return obj.map(function(item) {
          return AppUtils.deepClone(item);
        });
      }
      
      var cloned = {};
      for (var key in obj) {
        if (obj.hasOwnProperty(key)) {
          cloned[key] = AppUtils.deepClone(obj[key]);
        }
      }
      
      return cloned;
    },
    
    /**
     * Get recruiting type display name
     */
    getRecruitingTypeDisplayName: function(type) {
      var types = {
        'ORA_REGULAR': 'Regular',
        'ORA_CONTINGENT': 'Contingent',
        'ORA_CAMPUS': 'Campus',
        'ORA_INTERNSHIP': 'Internship'
      };
      
      return types[type] || type;
    },
    
    /**
     * Get job type display name
     */
    getJobTypeDisplayName: function(type) {
      var types = {
        'ORA_REGULAR': 'Regular',
        'ORA_INTERNSHIP': 'Internship',
        'ORA_CONTRACT': 'Contract',
        'ORA_TEMPORARY': 'Temporary'
      };
      
      return types[type] || type;
    },
    
    /**
     * Generate unique ID
     */
    generateId: function() {
      return 'id_' + Math.random().toString(36).substr(2, 9);
    }
  };
  
  return AppUtils;
});
