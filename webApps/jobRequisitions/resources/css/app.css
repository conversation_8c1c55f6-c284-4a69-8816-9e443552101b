/* Oracle VBCS Job Requisitions Application Styles */

/* Main Layout */
.demo-page-content-area {
  padding: 20px 0;
}

.demo-info-section {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  margin-top: 20px;
  border-left: 4px solid #007db8;
}

.demo-info-section h3 {
  margin-top: 0;
  color: #007db8;
}

/* Table Styles */
.oj-table-stretch {
  width: 100%;
}

.oj-table th,
.oj-table td {
  padding: 12px 8px;
}

/* Badge Styles */
.oj-badge {
  display: inline-block;
  padding: 0.25em 0.6em;
  font-size: 75%;
  font-weight: 700;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25rem;
  color: white;
}

.oj-badge-info {
  background-color: #17a2b8;
}

.oj-badge-success {
  background-color: #28a745;
}

.oj-badge-warning {
  background-color: #ffc107;
  color: #212529;
}

.oj-badge-danger {
  background-color: #dc3545;
}

.oj-badge-secondary {
  background-color: #6c757d;
}

/* Form Styles */
.oj-form-layout {
  max-width: 800px;
}

.oj-form-control-group {
  margin-bottom: 1rem;
}

/* Button Styles */
.oj-button-primary {
  background-color: #007db8;
  border-color: #007db8;
}

.oj-button-primary:hover {
  background-color: #006ba6;
  border-color: #006ba6;
}

/* Loading States */
.loading-overlay {
  position: relative;
}

.loading-overlay::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 1000;
}

/* Search Bar */
.search-container {
  margin-bottom: 20px;
}

.search-container .oj-inputtext {
  min-width: 300px;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 8px;
}

.action-buttons .oj-button {
  min-width: auto;
  padding: 6px 12px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .oj-form-layout {
    max-columns: 1;
  }
  
  .oj-flex {
    flex-direction: column;
  }
  
  .oj-flex-item {
    margin-bottom: 10px;
  }
  
  .search-container .oj-inputtext {
    min-width: 100%;
  }
}

/* Status Indicators */
.status-active {
  color: #28a745;
  font-weight: bold;
}

.status-inactive {
  color: #dc3545;
  font-weight: bold;
}

.status-pending {
  color: #ffc107;
  font-weight: bold;
}

/* Dialog Styles */
.oj-dialog .oj-dialog-body {
  padding: 20px;
}

.oj-dialog .oj-dialog-footer {
  padding: 15px 20px;
  text-align: right;
}

.oj-dialog .oj-dialog-footer .oj-button {
  margin-left: 10px;
}

/* Validation Styles */
.oj-invalid {
  border-color: #dc3545;
}

.oj-warning {
  border-color: #ffc107;
}

/* Progress Indicators */
.oj-progress-bar {
  margin: 10px 0;
}

/* Pagination */
.oj-paging-control {
  margin-top: 20px;
}

/* Utility Classes */
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.margin-top {
  margin-top: 1rem;
}

.margin-bottom {
  margin-bottom: 1rem;
}

.padding {
  padding: 1rem;
}

.no-margin {
  margin: 0;
}

.no-padding {
  padding: 0;
}

/* Oracle JET Theme Overrides */
.oj-hybrid-padding {
  padding: 20px;
}

.oj-flex-items-pad > .oj-flex-item {
  padding: 0 10px;
}

.oj-flex-items-pad > .oj-flex-item:first-child {
  padding-left: 0;
}

.oj-flex-items-pad > .oj-flex-item:last-child {
  padding-right: 0;
}
