{"id": "main", "description": "Main application flow for job requisitions", "defaultPage": "main-start", "variables": {"globalMessage": {"type": "string", "defaultValue": "", "description": "Global message for user notifications"}, "globalMessageType": {"type": "string", "defaultValue": "info", "description": "Type of global message: info, success, warning, error"}, "showGlobalMessage": {"type": "boolean", "defaultValue": false, "description": "Whether to show global message"}}, "types": {"JobRequisition": {"$ref": "../../types/JobRequisition.json"}}, "imports": {"components": {"oj-messages": {"path": "ojs/ojmessages"}}}, "chains": {"showMessage": {"description": "Show global message to user", "root": "setMessage", "actions": {"setMessage": {"module": "vb/action/builtin/assignVariablesAction", "parameters": {"variables": {"globalMessage": "{{ $chain.parameters.message }}", "globalMessageType": "{{ $chain.parameters.type || 'info' }}", "showGlobalMessage": true}}, "outcomes": {"success": "autoHideMessage"}}, "autoHideMessage": {"module": "vb/action/builtin/callFunctionAction", "parameters": {"function": "setTimeout", "parameters": ["{{ function() { $flow.variables.showGlobalMessage = false; } }}", 5000]}}}}, "hideMessage": {"description": "Hide global message", "root": "hideMessage", "actions": {"hideMessage": {"module": "vb/action/builtin/assignVariablesAction", "parameters": {"variables": {"showGlobalMessage": false, "globalMessage": ""}}}}}}}