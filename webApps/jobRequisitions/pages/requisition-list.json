{"id": "requisition-list", "title": "Job Requisitions List", "description": "List and manage job requisitions", "variables": {"requisitionsADP": {"type": "vb/ArrayDataProvider", "defaultValue": {"data": "{{ $page.variables.requisitionsList }}", "keyAttributes": "RequisitionId"}}, "requisitionsList": {"type": "JobRequisition[]", "defaultValue": []}, "isLoading": {"type": "boolean", "defaultValue": false}, "searchQuery": {"type": "string", "defaultValue": ""}, "selectedRequisition": {"type": "JobRequisition", "defaultValue": null}, "showDeleteDialog": {"type": "boolean", "defaultValue": false}, "currentPage": {"type": "number", "defaultValue": 0}, "pageSize": {"type": "number", "defaultValue": 25}, "totalCount": {"type": "number", "defaultValue": 0}}, "imports": {"components": {"oj-table": {"path": "ojs/ojtable"}, "oj-input-text": {"path": "ojs/ojinputtext"}, "oj-button": {"path": "ojs/ojbutton"}, "oj-dialog": {"path": "ojs/ojdialog"}, "oj-bind-text": {"path": "ojs/ojbindtext"}, "oj-flex": {"path": "ojs/ojflex"}, "oj-flex-item": {"path": "ojs/ojflex"}, "oj-progress-bar": {"path": "ojs/ojprogressbar"}, "oj-paging-control": {"path": "ojs/ojpagingcontrol"}}}, "chains": {"loadRequisitions": {"description": "Load job requisitions from Oracle HCM API", "root": "setLoadingTrue", "actions": {"setLoadingTrue": {"module": "vb/action/builtin/assignVariablesAction", "parameters": {"variables": {"isLoading": true}}, "outcomes": {"success": "callRestAPI"}}, "callRestAPI": {"module": "vb/action/builtin/restAction", "parameters": {"endpoint": "OracleHCMService/getJobRequisitions", "requestType": "GET", "parameters": {"limit": "{{ $page.variables.pageSize }}", "offset": "{{ $page.variables.currentPage * $page.variables.pageSize }}", "totalResults": true, "q": "{{ $page.variables.searchQuery ? 'Title like \\'%' + $page.variables.searchQuery + '%\\'' : '' }}"}}, "outcomes": {"success": "processResponse", "failure": "handleError"}}, "processResponse": {"module": "vb/action/builtin/assignVariablesAction", "parameters": {"variables": {"requisitionsList": "{{ $chain.results.callRestAPI.body.items }}", "totalCount": "{{ $chain.results.callRestAPI.body.totalResults }}", "isLoading": false}}}, "handleError": {"module": "vb/action/builtin/assignVariablesAction", "parameters": {"variables": {"isLoading": false}}}}}, "searchRequisitions": {"description": "Search job requisitions", "root": "resetPage", "actions": {"resetPage": {"module": "vb/action/builtin/assignVariablesAction", "parameters": {"variables": {"currentPage": 0}}, "outcomes": {"success": "loadRequisitions"}}, "loadRequisitions": {"module": "vb/action/builtin/callChainAction", "parameters": {"chain": "loadRequisitions"}}}}, "createRequisition": {"description": "Navigate to create new requisition", "root": "navigateToDetail", "actions": {"navigateToDetail": {"module": "vb/action/builtin/navigateToPageAction", "parameters": {"page": "requisition-detail", "params": {"mode": "create"}}}}}, "editRequisition": {"description": "Navigate to edit requisition", "root": "navigateToDetail", "actions": {"navigateToDetail": {"module": "vb/action/builtin/navigateToPageAction", "parameters": {"page": "requisition-detail", "params": {"mode": "edit", "id": "{{ $current.data.RequisitionId }}"}}}}}, "confirmDelete": {"description": "Show delete confirmation dialog", "root": "setSelectedRequisition", "actions": {"setSelectedRequisition": {"module": "vb/action/builtin/assignVariablesAction", "parameters": {"variables": {"selectedRequisition": "{{ $current.data }}", "showDeleteDialog": true}}}}}, "deleteRequisition": {"description": "Delete the selected requisition", "root": "callDeleteAPI", "actions": {"callDeleteAPI": {"module": "vb/action/builtin/restAction", "parameters": {"endpoint": "OracleHCMService/deleteJobRequisition", "requestType": "DELETE", "parameters": {"id": "{{ $page.variables.selectedRequisition.RequisitionId }}"}}, "outcomes": {"success": "closeDialogAndRefresh", "failure": "handleDeleteError"}}, "closeDialogAndRefresh": {"module": "vb/action/builtin/assignVariablesAction", "parameters": {"variables": {"showDeleteDialog": false, "selectedRequisition": null}}, "outcomes": {"success": "refreshList"}}, "refreshList": {"module": "vb/action/builtin/callChainAction", "parameters": {"chain": "loadRequisitions"}}, "handleDeleteError": {"module": "vb/action/builtin/assignVariablesAction", "parameters": {"variables": {"showDeleteDialog": false}}}}}, "cancelDelete": {"description": "Cancel delete operation", "root": "closeDialog", "actions": {"closeDialog": {"module": "vb/action/builtin/assignVariablesAction", "parameters": {"variables": {"showDeleteDialog": false, "selectedRequisition": null}}}}}, "changePage": {"description": "Handle page change", "root": "updatePage", "actions": {"updatePage": {"module": "vb/action/builtin/assignVariablesAction", "parameters": {"variables": {"currentPage": "{{ $event.detail.value }}"}}, "outcomes": {"success": "loadRequisitions"}}, "loadRequisitions": {"module": "vb/action/builtin/callChainAction", "parameters": {"chain": "loadRequisitions"}}}}}, "eventListeners": {"vbEnter": {"chains": [{"chainId": "loadRequisitions"}]}}}