<div class="oj-hybrid-padding">
  <div class="oj-flex oj-flex-items-pad">
    <div class="oj-flex-item">
      <h1>
        <oj-bind-text value="Oracle HCM Job Requisitions Manager"></oj-bind-text>
      </h1>
      
      <div class="demo-page-content-area">
        <p>Welcome to the Oracle HCM Job Requisitions Management System. This application allows you to:</p>
        
        <ul>
          <li>View and search job requisitions</li>
          <li>Create new job requisitions</li>
          <li>Edit existing requisitions</li>
          <li>Delete requisitions</li>
          <li>Manage requisition workflow states</li>
        </ul>
        
        <div class="oj-flex oj-flex-items-pad">
          <div class="oj-flex-item">
            <oj-button 
              id="viewRequisitionsBtn"
              class="oj-button-primary"
              on-oj-action="[[$listeners.navigateToList]]">
              <span slot="startIcon" class="oj-ux-ico-list"></span>
              View Job Requisitions
            </oj-button>
          </div>
        </div>
        
        <div class="demo-info-section">
          <h3>System Information</h3>
          <p><strong>API Endpoint:</strong> <oj-bind-text value="[[$application.variables.oracleHCMBaseURL]]"></oj-bind-text></p>
          <p><strong>Current User:</strong> <oj-bind-text value="[[$application.variables.currentUser]]"></oj-bind-text></p>
        </div>
      </div>
    </div>
  </div>
</div>
