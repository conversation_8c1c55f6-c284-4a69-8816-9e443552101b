<div class="oj-hybrid-padding">
  <div class="oj-flex oj-flex-items-pad">
    <div class="oj-flex-item">
      <h1>
        <oj-bind-text value="Job Requisitions"></oj-bind-text>
      </h1>
      
      <!-- Search and Actions Bar -->
      <div class="oj-flex oj-flex-items-pad oj-margin-bottom">
        <div class="oj-flex-item oj-flex-grow-1">
          <oj-input-text
            id="searchInput"
            placeholder="Search by title..."
            value="{{ $page.variables.searchQuery }}"
            on-value-changed="[[$listeners.searchRequisitions]]">
            <span slot="start" class="oj-ux-ico-search"></span>
          </oj-input-text>
        </div>
        <div class="oj-flex-item">
          <oj-button
            id="createBtn"
            class="oj-button-primary"
            on-oj-action="[[$listeners.createRequisition]]">
            <span slot="startIcon" class="oj-ux-ico-plus"></span>
            Create New Requisition
          </oj-button>
        </div>
      </div>
      
      <!-- Loading Indicator -->
      <div class="oj-flex oj-flex-items-pad" data-oj-binding-provider="none" style="display: {{ $page.variables.isLoading ? 'flex' : 'none' }}">
        <div class="oj-flex-item">
          <oj-progress-bar value="-1"></oj-progress-bar>
          <p>Loading job requisitions...</p>
        </div>
      </div>
      
      <!-- Requisitions Table -->
      <div data-oj-binding-provider="none" style="display: {{ !$page.variables.isLoading ? 'block' : 'none' }}">
        <oj-table
          id="requisitionsTable"
          data="[[ $page.variables.requisitionsADP ]]"
          columns='[
            {
              "headerText": "Requisition #",
              "field": "RequisitionNumber",
              "template": "requisitionNumberTemplate"
            },
            {
              "headerText": "Title",
              "field": "Title",
              "template": "titleTemplate"
            },
            {
              "headerText": "Type",
              "field": "RecruitingType",
              "template": "typeTemplate"
            },
            {
              "headerText": "State",
              "field": "StateName",
              "template": "stateTemplate"
            },
            {
              "headerText": "Phase",
              "field": "PhaseName",
              "template": "phaseTemplate"
            },
            {
              "headerText": "Openings",
              "field": "NumberOfOpenings",
              "template": "openingsTemplate"
            },
            {
              "headerText": "Hired",
              "field": "HiredCount",
              "template": "hiredTemplate"
            },
            {
              "headerText": "Actions",
              "template": "actionsTemplate",
              "sortable": "disabled"
            }
          ]'
          class="oj-table-stretch">
          
          <!-- Templates -->
          <template slot="requisitionNumberTemplate" data-oj-as="cell">
            <oj-bind-text value="[[ cell.data ]]"></oj-bind-text>
          </template>
          
          <template slot="titleTemplate" data-oj-as="cell">
            <oj-bind-text value="[[ cell.data ]]"></oj-bind-text>
          </template>
          
          <template slot="typeTemplate" data-oj-as="cell">
            <span class="oj-badge oj-badge-info">
              <oj-bind-text value="[[ cell.data ]]"></oj-bind-text>
            </span>
          </template>
          
          <template slot="stateTemplate" data-oj-as="cell">
            <span class="oj-badge" 
                  data-oj-binding-provider="none"
                  style="background-color: {{ cell.data === 'In Progress' ? '#28a745' : cell.data === 'Expired' ? '#dc3545' : '#6c757d' }}">
              <oj-bind-text value="[[ cell.data ]]"></oj-bind-text>
            </span>
          </template>
          
          <template slot="phaseTemplate" data-oj-as="cell">
            <oj-bind-text value="[[ cell.data ]]"></oj-bind-text>
          </template>
          
          <template slot="openingsTemplate" data-oj-as="cell">
            <oj-bind-text value="[[ cell.data || 'Unlimited' ]]"></oj-bind-text>
          </template>
          
          <template slot="hiredTemplate" data-oj-as="cell">
            <oj-bind-text value="[[ cell.data || 0 ]]"></oj-bind-text>
          </template>
          
          <template slot="actionsTemplate" data-oj-as="cell">
            <div class="oj-flex oj-flex-items-pad">
              <div class="oj-flex-item">
                <oj-button
                  size="sm"
                  display="icons"
                  on-oj-action="[[$listeners.editRequisition]]"
                  title="Edit Requisition">
                  <span slot="startIcon" class="oj-ux-ico-edit"></span>
                </oj-button>
              </div>
              <div class="oj-flex-item">
                <oj-button
                  size="sm"
                  display="icons"
                  chroming="danger"
                  on-oj-action="[[$listeners.confirmDelete]]"
                  title="Delete Requisition">
                  <span slot="startIcon" class="oj-ux-ico-delete"></span>
                </oj-button>
              </div>
            </div>
          </template>
        </oj-table>
        
        <!-- Pagination -->
        <div class="oj-flex oj-flex-items-pad oj-margin-top">
          <div class="oj-flex-item">
            <oj-paging-control
              id="pagingControl"
              data="[[ $page.variables.requisitionsADP ]]"
              page-size="{{ $page.variables.pageSize }}"
              on-page-changed="[[$listeners.changePage]]">
            </oj-paging-control>
          </div>
        </div>
      </div>
      
      <!-- Delete Confirmation Dialog -->
      <oj-dialog
        id="deleteDialog"
        dialog-title="Confirm Delete"
        opened="{{ $page.variables.showDeleteDialog }}"
        modality="modal">
        <div slot="body">
          <p>Are you sure you want to delete the job requisition:</p>
          <p><strong><oj-bind-text value="[[ $page.variables.selectedRequisition.RequisitionNumber ]]"></oj-bind-text> - <oj-bind-text value="[[ $page.variables.selectedRequisition.Title ]]"></oj-bind-text></strong></p>
          <p>This action cannot be undone.</p>
        </div>
        <div slot="footer">
          <oj-button
            id="cancelDeleteBtn"
            on-oj-action="[[$listeners.cancelDelete]]">
            Cancel
          </oj-button>
          <oj-button
            id="confirmDeleteBtn"
            class="oj-button-primary"
            chroming="danger"
            on-oj-action="[[$listeners.deleteRequisition]]">
            Delete
          </oj-button>
        </div>
      </oj-dialog>
    </div>
  </div>
</div>
