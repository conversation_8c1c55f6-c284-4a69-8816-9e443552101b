<div class="oj-hybrid-padding">
  <div class="oj-flex oj-flex-items-pad">
    <div class="oj-flex-item">
      <h1>
        <oj-bind-text value="{{ $page.variables.mode === 'create' ? 'Create New Job Requisition' : 'Edit Job Requisition' }}"></oj-bind-text>
      </h1>
      
      <!-- Loading Indicator -->
      <div class="oj-flex oj-flex-items-pad" data-oj-binding-provider="none" style="display: {{ $page.variables.isLoading ? 'flex' : 'none' }}">
        <div class="oj-flex-item">
          <oj-progress-bar value="-1"></oj-progress-bar>
          <p>Loading requisition details...</p>
        </div>
      </div>
      
      <!-- Form -->
      <div data-oj-binding-provider="none" style="display: {{ !$page.variables.isLoading ? 'block' : 'none' }}">
        <oj-validation-group id="validationGroup">
          <oj-form-layout max-columns="2" label-edge="start" label-width="33%">
            
            <!-- Basic Information -->
            <oj-input-text
              id="requisitionNumber"
              label-hint="Requisition Number"
              value="{{ $page.variables.requisition.RequisitionNumber }}"
              required="true"
              placeholder="Enter requisition number">
            </oj-input-text>
            
            <oj-input-text
              id="title"
              label-hint="Job Title"
              value="{{ $page.variables.requisition.Title }}"
              required="true"
              placeholder="Enter job title">
            </oj-input-text>
            
            <oj-select-single
              id="recruitingType"
              label-hint="Recruiting Type"
              value="{{ $page.variables.requisition.RecruitingType }}"
              data="{{ $page.variables.recruitingTypes }}"
              item-text="label"
              value-item="value"
              required="true">
            </oj-select-single>
            
            <oj-select-single
              id="jobType"
              label-hint="Job Type"
              value="{{ $page.variables.requisition.JobType }}"
              data="{{ $page.variables.jobTypes }}"
              item-text="label"
              value-item="value">
            </oj-select-single>
            
            <oj-input-number
              id="numberOfOpenings"
              label-hint="Number of Openings"
              value="{{ $page.variables.requisition.NumberOfOpenings }}"
              min="1"
              required="true">
            </oj-input-number>
            
            <oj-select-single
              id="businessJustification"
              label-hint="Business Justification"
              value="{{ $page.variables.requisition.BusinessJustification }}"
              data="{{ $page.variables.businessJustifications }}"
              item-text="label"
              value-item="value">
            </oj-select-single>
            
            <oj-select-single
              id="fullTimeOrPartTime"
              label-hint="Employment Type"
              value="{{ $page.variables.requisition.FullTimeOrPartTime }}"
              data="{{ $page.variables.employmentTypes }}"
              item-text="label"
              value-item="value">
            </oj-select-single>
            
            <oj-input-text
              id="educationLevel"
              label-hint="Education Level"
              value="{{ $page.variables.requisition.EducationLevel }}"
              placeholder="Enter education level">
            </oj-input-text>
            
            <!-- Compensation -->
            <oj-input-number
              id="minimumSalary"
              label-hint="Minimum Salary"
              value="{{ $page.variables.requisition.MinimumSalary }}"
              min="0"
              converter='{"type": "number", "options": {"style": "currency", "currency": "USD"}}'>
            </oj-input-number>
            
            <oj-input-number
              id="maximumSalary"
              label-hint="Maximum Salary"
              value="{{ $page.variables.requisition.MaximumSalary }}"
              min="0"
              converter='{"type": "number", "options": {"style": "currency", "currency": "USD"}}'>
            </oj-input-number>
            
            <oj-input-text
              id="compensationCurrency"
              label-hint="Currency"
              value="{{ $page.variables.requisition.CompensationCurrency }}"
              placeholder="USD">
            </oj-input-text>
            
            <!-- Descriptions -->
            <div class="oj-form-control-group" style="grid-column: 1 / -1;">
              <oj-text-area
                id="internalDescription"
                label-hint="Internal Description"
                value="{{ $page.variables.requisition.InternalDescription }}"
                rows="4"
                placeholder="Enter job description for internal candidates">
              </oj-text-area>
            </div>
            
            <div class="oj-form-control-group" style="grid-column: 1 / -1;">
              <oj-text-area
                id="externalDescription"
                label-hint="External Description"
                value="{{ $page.variables.requisition.ExternalDescription }}"
                rows="4"
                placeholder="Enter job description for external candidates">
              </oj-text-area>
            </div>
            
          </oj-form-layout>
        </oj-validation-group>
        
        <!-- Action Buttons -->
        <div class="oj-flex oj-flex-items-pad oj-margin-top">
          <div class="oj-flex-item">
            <oj-button
              id="saveBtn"
              class="oj-button-primary"
              disabled="{{ $page.variables.isSaving }}"
              on-oj-action="[[$listeners.saveRequisition]]">
              <span slot="startIcon" class="oj-ux-ico-save"></span>
              <oj-bind-text value="{{ $page.variables.mode === 'create' ? 'Create Requisition' : 'Update Requisition' }}"></oj-bind-text>
            </oj-button>
          </div>
          <div class="oj-flex-item">
            <oj-button
              id="cancelBtn"
              disabled="{{ $page.variables.isSaving }}"
              on-oj-action="[[$listeners.cancelEdit]]">
              Cancel
            </oj-button>
          </div>
        </div>
        
        <!-- Saving Indicator -->
        <div class="oj-flex oj-flex-items-pad oj-margin-top" data-oj-binding-provider="none" style="display: {{ $page.variables.isSaving ? 'flex' : 'none' }}">
          <div class="oj-flex-item">
            <oj-progress-bar value="-1"></oj-progress-bar>
            <p><oj-bind-text value="{{ $page.variables.mode === 'create' ? 'Creating requisition...' : 'Updating requisition...' }}"></oj-bind-text></p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
