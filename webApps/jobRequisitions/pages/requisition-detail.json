{"id": "requisition-detail", "title": "Job Requisition Details", "description": "Create or edit job requisition", "variables": {"mode": {"type": "string", "defaultValue": "create", "description": "Mode: create or edit"}, "requisitionId": {"type": "string", "defaultValue": null, "description": "Requisition ID for edit mode"}, "requisition": {"type": "JobRequisition", "defaultValue": {"RequisitionNumber": "", "Title": "", "RecruitingType": "ORA_REGULAR", "NumberOfOpenings": 1, "BusinessJustification": "ORA_NEW_POSITION", "FullTimeOrPartTime": "FULL_TIME", "JobType": "ORA_REGULAR", "EducationLevel": "12", "MinimumSalary": 0, "MaximumSalary": 0, "CompensationCurrency": "USD", "InternalDescription": "", "ExternalDescription": ""}}, "isLoading": {"type": "boolean", "defaultValue": false}, "isSaving": {"type": "boolean", "defaultValue": false}, "validationErrors": {"type": "object", "defaultValue": {}}, "recruitingTypes": {"type": "array", "defaultValue": [{"value": "ORA_REGULAR", "label": "Regular"}, {"value": "ORA_CONTINGENT", "label": "Contingent"}, {"value": "ORA_CAMPUS", "label": "Campus"}, {"value": "ORA_INTERNSHIP", "label": "Internship"}]}, "jobTypes": {"type": "array", "defaultValue": [{"value": "ORA_REGULAR", "label": "Regular"}, {"value": "ORA_INTERNSHIP", "label": "Internship"}, {"value": "ORA_CONTRACT", "label": "Contract"}, {"value": "ORA_TEMPORARY", "label": "Temporary"}]}, "employmentTypes": {"type": "array", "defaultValue": [{"value": "FULL_TIME", "label": "Full Time"}, {"value": "PART_TIME", "label": "Part Time"}]}, "businessJustifications": {"type": "array", "defaultValue": [{"value": "ORA_NEW_POSITION", "label": "New Position"}, {"value": "ORA_REPLACEMENT", "label": "Replacement"}, {"value": "ORA_EXPANSION", "label": "Business Expansion"}]}}, "imports": {"components": {"oj-form-layout": {"path": "ojs/ojformlayout"}, "oj-input-text": {"path": "ojs/ojinputtext"}, "oj-input-number": {"path": "ojs/ojinputnumber"}, "oj-text-area": {"path": "ojs/ojtextarea"}, "oj-select-single": {"path": "ojs/ojselectsingle"}, "oj-button": {"path": "ojs/ojbutton"}, "oj-bind-text": {"path": "ojs/ojbindtext"}, "oj-flex": {"path": "ojs/ojflex"}, "oj-flex-item": {"path": "ojs/ojflex"}, "oj-progress-bar": {"path": "ojs/ojprogressbar"}, "oj-validation-group": {"path": "ojs/ojvalidationgroup"}}}, "chains": {"loadRequisition": {"description": "Load requisition for edit mode", "root": "checkMode", "actions": {"checkMode": {"module": "vb/action/builtin/ifAction", "parameters": {"condition": "[[ $page.variables.mode === 'edit' && $page.variables.requisitionId ]]"}, "outcomes": {"true": "setLoadingTrue", "false": "skipLoad"}}, "setLoadingTrue": {"module": "vb/action/builtin/assignVariablesAction", "parameters": {"variables": {"isLoading": true}}, "outcomes": {"success": "callGetAPI"}}, "callGetAPI": {"module": "vb/action/builtin/restAction", "parameters": {"endpoint": "OracleHCMService/getJobRequisition", "requestType": "GET", "parameters": {"id": "{{ $page.variables.requisitionId }}"}}, "outcomes": {"success": "processResponse", "failure": "handleError"}}, "processResponse": {"module": "vb/action/builtin/assignVariablesAction", "parameters": {"variables": {"requisition": "{{ $chain.results.callGetAPI.body }}", "isLoading": false}}}, "handleError": {"module": "vb/action/builtin/assignVariablesAction", "parameters": {"variables": {"isLoading": false}}}, "skipLoad": {"module": "vb/action/builtin/assignVariablesAction", "parameters": {"variables": {"isLoading": false}}}}}, "saveRequisition": {"description": "Save or update job requisition", "root": "validateForm", "actions": {"validateForm": {"module": "vb/action/builtin/callComponentMethodAction", "parameters": {"component": "{{ document.getElementById('validationGroup') }}", "method": "validate"}, "outcomes": {"success": "checkValidation", "failure": "validationFailed"}}, "checkValidation": {"module": "vb/action/builtin/ifAction", "parameters": {"condition": "[[ $chain.results.validateForm === 'valid' ]]"}, "outcomes": {"true": "setSavingTrue", "false": "validationFailed"}}, "setSavingTrue": {"module": "vb/action/builtin/assignVariablesAction", "parameters": {"variables": {"isSaving": true}}, "outcomes": {"success": "checkMode"}}, "checkMode": {"module": "vb/action/builtin/ifAction", "parameters": {"condition": "[[ $page.variables.mode === 'create' ]]"}, "outcomes": {"true": "callCreateAPI", "false": "callUpdateAPI"}}, "callCreateAPI": {"module": "vb/action/builtin/restAction", "parameters": {"endpoint": "OracleHCMService/createJobRequisition", "requestType": "POST", "body": "{{ $page.variables.requisition }}"}, "outcomes": {"success": "saveSuccess", "failure": "saveError"}}, "callUpdateAPI": {"module": "vb/action/builtin/restAction", "parameters": {"endpoint": "OracleHCMService/updateJobRequisition", "requestType": "PATCH", "parameters": {"id": "{{ $page.variables.requisition.RequisitionId }}"}, "body": "{{ $page.variables.requisition }}"}, "outcomes": {"success": "saveSuccess", "failure": "saveError"}}, "saveSuccess": {"module": "vb/action/builtin/assignVariablesAction", "parameters": {"variables": {"isSaving": false}}, "outcomes": {"success": "navigateBack"}}, "saveError": {"module": "vb/action/builtin/assignVariablesAction", "parameters": {"variables": {"isSaving": false}}}, "validationFailed": {"module": "vb/action/builtin/assignVariablesAction", "parameters": {"variables": {}}}, "navigateBack": {"module": "vb/action/builtin/navigateToPageAction", "parameters": {"page": "requisition-list"}}}}, "cancelEdit": {"description": "Cancel editing and go back", "root": "navigateBack", "actions": {"navigateBack": {"module": "vb/action/builtin/navigateToPageAction", "parameters": {"page": "requisition-list"}}}}}, "eventListeners": {"vbEnter": {"chains": [{"chainId": "loadRequisition"}]}}}