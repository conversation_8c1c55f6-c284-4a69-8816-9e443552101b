/**
 * Configuration validation script for Oracle HCM VBCS Application
 */

const fs = require('fs');
const path = require('path');

function validateConfig() {
  console.log('🔍 Validating Oracle HCM VBCS Application Configuration...\n');
  
  const errors = [];
  const warnings = [];
  
  // Check main application.json
  try {
    const appConfig = JSON.parse(fs.readFileSync('application.json', 'utf8'));
    
    if (!appConfig.id) {
      errors.push('Application ID is missing in application.json');
    }
    
    if (!appConfig.variables || !appConfig.variables.oracleHCMBaseURL) {
      errors.push('Oracle HCM Base URL variable is missing');
    }
    
    if (!appConfig.variables || !appConfig.variables.authToken) {
      warnings.push('Auth token variable should be configured for production');
    }
    
    console.log('✅ application.json structure is valid');
  } catch (e) {
    errors.push('Invalid application.json: ' + e.message);
  }
  
  // Check required directories
  const requiredDirs = [
    'webApps/jobRequisitions/pages',
    'webApps/jobRequisitions/services',
    'webApps/jobRequisitions/types',
    'webApps/jobRequisitions/resources/css',
    'webApps/jobRequisitions/resources/js'
  ];
  
  requiredDirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      errors.push(`Required directory missing: ${dir}`);
    } else {
      console.log(`✅ Directory exists: ${dir}`);
    }
  });
  
  // Check required files
  const requiredFiles = [
    'webApps/jobRequisitions/app-flow.json',
    'webApps/jobRequisitions/pages/main-start.json',
    'webApps/jobRequisitions/pages/main-start.html',
    'webApps/jobRequisitions/pages/requisition-list.json',
    'webApps/jobRequisitions/pages/requisition-list.html',
    'webApps/jobRequisitions/pages/requisition-detail.json',
    'webApps/jobRequisitions/pages/requisition-detail.html',
    'webApps/jobRequisitions/services/OracleHCMService.json',
    'webApps/jobRequisitions/types/JobRequisition.json',
    'webApps/jobRequisitions/resources/css/app.css',
    'webApps/jobRequisitions/resources/js/app.js'
  ];
  
  requiredFiles.forEach(file => {
    if (!fs.existsSync(file)) {
      errors.push(`Required file missing: ${file}`);
    } else {
      console.log(`✅ File exists: ${file}`);
    }
  });
  
  // Validate service configuration
  try {
    const serviceConfig = JSON.parse(fs.readFileSync('webApps/jobRequisitions/services/OracleHCMService.json', 'utf8'));
    
    if (!serviceConfig.endpoints) {
      errors.push('Service endpoints not defined');
    } else {
      const requiredEndpoints = ['getJobRequisitions', 'getJobRequisition', 'createJobRequisition', 'updateJobRequisition', 'deleteJobRequisition'];
      requiredEndpoints.forEach(endpoint => {
        if (!serviceConfig.endpoints[endpoint]) {
          errors.push(`Missing service endpoint: ${endpoint}`);
        }
      });
    }
    
    console.log('✅ Service configuration is valid');
  } catch (e) {
    errors.push('Invalid service configuration: ' + e.message);
  }
  
  // Summary
  console.log('\n📊 Validation Summary:');
  console.log(`✅ Passed: ${requiredDirs.length + requiredFiles.length - errors.length} checks`);
  
  if (warnings.length > 0) {
    console.log(`⚠️  Warnings: ${warnings.length}`);
    warnings.forEach(warning => console.log(`   - ${warning}`));
  }
  
  if (errors.length > 0) {
    console.log(`❌ Errors: ${errors.length}`);
    errors.forEach(error => console.log(`   - ${error}`));
    process.exit(1);
  } else {
    console.log('\n🎉 Configuration validation passed! Ready for deployment to VBCS.');
  }
}

if (require.main === module) {
  validateConfig();
}

module.exports = { validateConfig };
