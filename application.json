{"id": "oracle-hcm-job-requisitions", "version": "1.0.0", "description": "Oracle HCM Job Requisitions CRUD Application", "displayName": "HCM Job Requisitions Manager", "defaultLanguage": "en", "supportedLanguages": ["en"], "icon": "oj-ux-ico-business-objects", "changeListeners": {}, "security": {"access": {"requiresAuthentication": true}}, "variables": {"oracleHCMBaseURL": {"type": "string", "defaultValue": "https://demo-hcm-instance.oraclecloud.com/hcmRestApi/resources/***********", "description": "Oracle HCM REST API base URL"}, "authToken": {"type": "string", "defaultValue": "Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJkZW1vX3VzZXIiLCJuYW1lIjoiRGVtbyBVc2VyIiwiaWF0IjoxNjM5NTc2ODAwLCJleHAiOjE2Mzk2NjMyMDB9.DEMO_TOKEN_FOR_TESTING", "description": "Authentication token for Oracle HCM API"}, "currentUser": {"type": "string", "defaultValue": "<EMAIL>", "description": "Current authenticated user"}, "vbcsInstanceURL": {"type": "string", "defaultValue": "https://demo-vbcs-instance.oraclecloud.com", "description": "Oracle VBCS instance URL for deployment"}, "deploymentEnvironment": {"type": "string", "defaultValue": "development", "description": "Current deployment environment"}}, "webApps": {"jobRequisitions": {"path": "webApps/jobRequisitions", "displayName": "Job Requisitions", "description": "Manage Oracle HCM Job Requisitions", "icon": "oj-ux-ico-business-objects", "defaultPage": "main-start"}}, "services": {"OracleHCMService": {"url": "{{$application.variables.oracleHCMBaseURL}}", "type": "REST", "description": "Oracle HCM Recruiting Cloud REST API Service"}}}