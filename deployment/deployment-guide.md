# Oracle VBCS Deployment Guide

## Overview
This guide provides step-by-step instructions for deploying the Oracle HCM Job Requisitions application to Oracle Visual Builder Cloud Service (VBCS).

## Prerequisites

### Oracle Cloud Access
- Oracle Cloud Infrastructure (OCI) account
- Oracle Visual Builder Cloud Service instance
- Oracle HCM Cloud instance with Recruiting module
- Appropriate user permissions for VBCS and HCM

### Development Environment
- Node.js 14+ installed
- Git for version control
- Web browser for VBCS console access

## Deployment Steps

### 1. Pre-Deployment Validation

Run the configuration validation:
```bash
npm run validate
```

This will check:
- Application structure integrity
- Required files presence
- Service configuration validity
- Dependency requirements

### 2. Environment Configuration

Update the following files with your actual Oracle Cloud details:

#### application.json
```json
{
  "variables": {
    "oracleHCMBaseURL": "https://your-actual-hcm-instance.oraclecloud.com/hcmRestApi/resources/***********",
    "authToken": "Bearer YOUR_ACTUAL_ACCESS_TOKEN",
    "currentUser": "<EMAIL>",
    "vbcsInstanceURL": "https://your-actual-vbcs-instance.oraclecloud.com"
  }
}
```

#### deployment-config.json
Update the environments section with your actual instance URLs and credentials.

### 3. Create Deployment Package

Generate the deployment package:
```bash
npm run package
```

This creates:
- `deployment/package/` - Structured application files
- `deployment/output/oracle-hcm-vbcs-app.zip` - Deployment archive

### 4. Deploy to VBCS

#### Option A: Automated Deployment (Recommended)
```bash
# Development environment
npm run deploy:dev

# Staging environment  
npm run deploy:staging

# Production environment
npm run deploy:prod
```

#### Option B: Manual VBCS Console Deployment

1. **Access VBCS Console**
   - Log into Oracle Cloud Infrastructure
   - Navigate to Visual Builder Cloud Service
   - Open your VBCS instance

2. **Import Application**
   - Click "Import" in the VBCS console
   - Upload `oracle-hcm-vbcs-app.zip`
   - Follow the import wizard

3. **Configure Services**
   - Navigate to Services tab
   - Configure OracleHCMService endpoints
   - Test service connectivity

4. **Deploy Application**
   - Click "Deploy" in VBCS console
   - Select target environment
   - Monitor deployment progress

### 5. Post-Deployment Configuration

#### Service Authentication
1. Configure OAuth2 credentials for HCM service
2. Test API connectivity
3. Verify user permissions

#### Application Testing
1. Access the deployed application URL
2. Test all CRUD operations:
   - List job requisitions
   - Create new requisition
   - Edit existing requisition
   - Delete requisition
3. Verify error handling and validation

## Environment-Specific Configurations

### Development Environment
- Debug mode enabled
- Relaxed security settings
- Test data and mock services
- Extended logging

### Staging Environment
- Production-like configuration
- Real HCM service integration
- Performance testing
- User acceptance testing

### Production Environment
- Optimized performance settings
- Full security configuration
- Production HCM integration
- Monitoring and alerting

## Troubleshooting

### Common Issues

#### Authentication Failures
- Verify OAuth2 token validity
- Check HCM instance accessibility
- Confirm user permissions
- Review CORS configuration

#### Service Connection Issues
- Test HCM API endpoints manually
- Verify network connectivity
- Check firewall rules
- Validate SSL certificates

#### Deployment Failures
- Review VBCS deployment logs
- Check application structure
- Verify file permissions
- Validate JSON configurations

### Debug Mode
Enable debug logging by setting:
```json
{
  "deployment": {
    "environment": "development",
    "settings": {
      "debug": true
    }
  }
}
```

### Log Files
- Deployment logs: `deployment/deployment.log`
- Application logs: Available in VBCS console
- Service logs: Check Oracle HCM audit logs

## Security Considerations

### Authentication
- Use OAuth2 for production environments
- Rotate access tokens regularly
- Implement proper session management
- Enable multi-factor authentication

### Data Protection
- Encrypt sensitive data in transit
- Implement proper access controls
- Regular security audits
- Compliance with data regulations

### Network Security
- Configure proper CORS policies
- Use HTTPS for all communications
- Implement rate limiting
- Monitor for suspicious activities

## Performance Optimization

### Application Performance
- Enable caching where appropriate
- Optimize API calls and pagination
- Implement lazy loading
- Monitor response times

### Service Performance
- Configure connection pooling
- Implement retry mechanisms
- Set appropriate timeouts
- Monitor service health

## Monitoring and Maintenance

### Application Monitoring
- Set up health checks
- Monitor user activity
- Track performance metrics
- Configure alerting

### Regular Maintenance
- Update dependencies regularly
- Review and update configurations
- Backup application data
- Test disaster recovery procedures

## Support and Documentation

### Oracle Resources
- Oracle VBCS Documentation
- Oracle HCM Cloud API Documentation
- Oracle Support Portal
- Oracle Cloud Community

### Application Support
- Check deployment logs for errors
- Review application configuration
- Test service connectivity
- Contact Oracle Support if needed

## Appendix

### Required Permissions
- VBCS Developer role
- HCM Recruiting functional privileges
- OCI resource management permissions

### API Endpoints Used
- GET /recruitingJobRequisitions
- GET /recruitingJobRequisitions/{id}
- POST /recruitingJobRequisitions
- PATCH /recruitingJobRequisitions/{id}
- DELETE /recruitingJobRequisitions/{id}

### File Structure
```
oracle-hcm-vbcs-app/
├── application.json
├── deployment-config.json
├── package.json
├── webApps/jobRequisitions/
│   ├── pages/
│   ├── services/
│   ├── types/
│   └── resources/
└── deployment/
    ├── deploy-to-vbcs.js
    ├── create-package.js
    └── deployment-guide.md
```
