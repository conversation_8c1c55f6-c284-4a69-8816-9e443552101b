{"manifestVersion": "1.0", "applicationId": "oracle-hcm-job-requisitions", "applicationVersion": "1.0.0", "deploymentTarget": "vbcs", "createdDate": "2025-06-18T00:00:00Z", "description": "Oracle HCM Job Requisitions CRUD Application - VBCS Deployment Package", "author": "Oracle VBCS Developer", "dependencies": {"oraclejet": "^14.0.0", "vbcs-runtime": "21.10.0"}, "deployment": {"environment": "development", "instanceURL": "https://demo-vbcs-dev.oraclecloud.com", "credentials": {"type": "oauth2", "clientId": "demo_client_id_12345", "clientSecret": "demo_client_secret_abcdef", "tokenEndpoint": "https://demo-vbcs-dev.oraclecloud.com/oauth2/token"}}, "applicationStructure": {"rootFiles": ["application.json", "deployment-config.json", "package.json"], "webApps": {"jobRequisitions": {"path": "webApps/jobRequisitions", "type": "spa", "entryPoint": "main-start", "requiredFiles": ["app-flow.json", "pages/main-start.json", "pages/main-start.html", "pages/requisition-list.json", "pages/requisition-list.html", "pages/requisition-detail.json", "pages/requisition-detail.html", "services/OracleHCMService.json", "types/JobRequisition.json", "resources/css/app.css", "resources/js/app.js"]}}}, "externalServices": {"OracleHCMService": {"type": "REST", "baseURL": "https://demo-hcm-dev.oraclecloud.com/hcmRestApi/resources/11.13.18.05", "authentication": "bearer", "testEndpoint": "/recruitingJobRequisitions?limit=1"}}, "deploymentSteps": ["validate-configuration", "package-application", "upload-to-vbcs", "configure-services", "deploy-application", "test-deployment"]}