#!/usr/bin/env node

/**
 * VBCS Package Creation Script
 * Creates a deployment-ready package for Oracle Visual Builder Cloud Service
 */

const fs = require('fs');
const path = require('path');
const archiver = require('archiver');

class VBCSPackager {
  constructor() {
    this.rootDir = path.join(__dirname, '..');
    this.packageDir = path.join(__dirname, 'package');
    this.outputDir = path.join(__dirname, 'output');
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] ${type.toUpperCase()}: ${message}`);
  }

  async createPackage() {
    this.log('📦 Creating VBCS deployment package...');

    try {
      // Clean and create directories
      await this.cleanDirectories();
      await this.createDirectories();

      // Copy application files
      await this.copyApplicationFiles();

      // Create package metadata
      await this.createPackageMetadata();

      // Create ZIP archive
      await this.createZipArchive();

      this.log('✅ VBCS package created successfully!');
      this.log(`📁 Package location: ${path.join(this.outputDir, 'oracle-hcm-vbcs-app.zip')}`);

    } catch (error) {
      this.log(`❌ Package creation failed: ${error.message}`, 'error');
      process.exit(1);
    }
  }

  async cleanDirectories() {
    this.log('🧹 Cleaning directories...');
    
    if (fs.existsSync(this.packageDir)) {
      fs.rmSync(this.packageDir, { recursive: true, force: true });
    }
    
    if (fs.existsSync(this.outputDir)) {
      fs.rmSync(this.outputDir, { recursive: true, force: true });
    }
  }

  async createDirectories() {
    this.log('📁 Creating package directories...');
    
    fs.mkdirSync(this.packageDir, { recursive: true });
    fs.mkdirSync(this.outputDir, { recursive: true });
  }

  async copyApplicationFiles() {
    this.log('📄 Copying application files...');

    // Root files
    const rootFiles = [
      'application.json',
      'deployment-config.json',
      'package.json',
      'README.md'
    ];

    rootFiles.forEach(file => {
      const sourcePath = path.join(this.rootDir, file);
      const destPath = path.join(this.packageDir, file);
      
      if (fs.existsSync(sourcePath)) {
        fs.copyFileSync(sourcePath, destPath);
        this.log(`✅ Copied ${file}`);
      } else {
        this.log(`⚠️ File not found: ${file}`, 'warn');
      }
    });

    // Copy webApps directory
    const webAppsSource = path.join(this.rootDir, 'webApps');
    const webAppsDest = path.join(this.packageDir, 'webApps');
    
    if (fs.existsSync(webAppsSource)) {
      this.copyDirectory(webAppsSource, webAppsDest);
      this.log('✅ Copied webApps directory');
    }

    // Copy scripts directory
    const scriptsSource = path.join(this.rootDir, 'scripts');
    const scriptsDest = path.join(this.packageDir, 'scripts');
    
    if (fs.existsSync(scriptsSource)) {
      this.copyDirectory(scriptsSource, scriptsDest);
      this.log('✅ Copied scripts directory');
    }
  }

  copyDirectory(source, destination) {
    if (!fs.existsSync(destination)) {
      fs.mkdirSync(destination, { recursive: true });
    }

    const items = fs.readdirSync(source);
    items.forEach(item => {
      const sourcePath = path.join(source, item);
      const destPath = path.join(destination, item);
      
      if (fs.statSync(sourcePath).isDirectory()) {
        this.copyDirectory(sourcePath, destPath);
      } else {
        fs.copyFileSync(sourcePath, destPath);
      }
    });
  }

  async createPackageMetadata() {
    this.log('📋 Creating package metadata...');

    const metadata = {
      packageVersion: "1.0.0",
      createdDate: new Date().toISOString(),
      packageType: "vbcs-application",
      applicationId: "oracle-hcm-job-requisitions",
      description: "Oracle HCM Job Requisitions CRUD Application",
      targetPlatform: "Oracle Visual Builder Cloud Service",
      minimumVBCSVersion: "21.10.0",
      dependencies: {
        "oraclejet": "^14.0.0"
      },
      files: this.getFileList(this.packageDir),
      deploymentInstructions: [
        "1. Import this package into Oracle VBCS",
        "2. Configure Oracle HCM service endpoints",
        "3. Update authentication credentials",
        "4. Test service connectivity",
        "5. Deploy to target environment"
      ]
    };

    const metadataPath = path.join(this.packageDir, 'package-metadata.json');
    fs.writeFileSync(metadataPath, JSON.stringify(metadata, null, 2));
    this.log('✅ Package metadata created');
  }

  getFileList(dir, fileList = [], basePath = '') {
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const itemPath = path.join(dir, item);
      const relativePath = path.join(basePath, item);
      
      if (fs.statSync(itemPath).isDirectory()) {
        this.getFileList(itemPath, fileList, relativePath);
      } else {
        fileList.push(relativePath);
      }
    });
    
    return fileList;
  }

  async createZipArchive() {
    this.log('🗜️ Creating ZIP archive...');

    return new Promise((resolve, reject) => {
      const output = fs.createWriteStream(path.join(this.outputDir, 'oracle-hcm-vbcs-app.zip'));
      const archive = archiver('zip', { zlib: { level: 9 } });

      output.on('close', () => {
        this.log(`✅ Archive created: ${archive.pointer()} total bytes`);
        resolve();
      });

      archive.on('error', (err) => {
        reject(err);
      });

      archive.pipe(output);
      archive.directory(this.packageDir, false);
      archive.finalize();
    });
  }
}

// Run packaging if script is executed directly
if (require.main === module) {
  const packager = new VBCSPackager();
  packager.createPackage().catch(error => {
    console.error('Packaging failed:', error);
    process.exit(1);
  });
}

module.exports = VBCSPackager;
