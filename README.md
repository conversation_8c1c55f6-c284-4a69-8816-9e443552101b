# Oracle HCM Job Requisitions VBCS Application

A full-stack web application built with Oracle Visual Builder Cloud Service (VBCS) for performing CRUD operations on job requisitions using the Oracle HCM Recruiting Cloud REST API.

## Features

- **List View**: Paginated table with search and filter capabilities
- **Create**: Form-based creation of new job requisitions
- **Read**: View detailed job requisition information
- **Update**: Edit existing job requisitions
- **Delete**: Remove job requisitions with confirmation
- **Authentication**: OAuth2/Bearer token support
- **Error Handling**: Comprehensive error handling and loading states
- **Responsive Design**: Mobile-friendly interface

## Architecture

### Technology Stack
- **Frontend**: Oracle Visual Builder Cloud Service (VBCS)
- **UI Framework**: Oracle JET (JavaScript Extension Toolkit)
- **Backend API**: Oracle HCM Recruiting Cloud REST API
- **Authentication**: OAuth2 Bearer Token / Basic Auth

### Project Structure
```
oracle-hcm-vbcs-app/
├── application.json                    # Main application configuration
├── webApps/
│   └── jobRequisitions/
│       ├── app-flow.json              # Web app configuration
│       ├── pages/                     # Page definitions
│       │   ├── main-start.json        # Landing page
│       │   ├── main-start.html        # Landing page template
│       │   ├── requisition-list.json  # List page logic
│       │   ├── requisition-list.html  # List page template
│       │   ├── requisition-detail.json # Form page logic
│       │   └── requisition-detail.html # Form page template
│       ├── flows/
│       │   └── main/
│       │       └── main-flow.json     # Main flow configuration
│       ├── services/
│       │   └── OracleHCMService.json  # REST service configuration
│       ├── types/
│       │   └── JobRequisition.json    # Data type definitions
│       └── resources/
│           ├── css/
│           │   └── app.css            # Custom styles
│           └── js/
│               └── app.js             # Utility functions
```

## API Integration

### Oracle HCM REST API Endpoints

The application integrates with the following Oracle HCM Recruiting Cloud REST API endpoints:

1. **GET /recruitingJobRequisitions**
   - List job requisitions with pagination and filtering
   - Supports query parameters: limit, offset, q, orderBy, totalResults

2. **GET /recruitingJobRequisitions/{id}**
   - Get a specific job requisition by ID

3. **POST /recruitingJobRequisitions**
   - Create a new job requisition

4. **PATCH /recruitingJobRequisitions/{id}**
   - Update an existing job requisition

5. **DELETE /recruitingJobRequisitions/{id}**
   - Delete a job requisition

### Authentication

The application supports Oracle HCM Cloud authentication methods:

- **Bearer Token**: OAuth2 access token
- **Basic Authentication**: Username/password (for development)

## Configuration

### Environment Variables

Update the following variables in `application.json`:

```json
{
  "variables": {
    "oracleHCMBaseURL": {
      "defaultValue": "https://your-hcm-instance.oraclecloud.com/hcmRestApi/resources/11.13.18.05"
    },
    "authToken": {
      "defaultValue": "Bearer YOUR_ACCESS_TOKEN"
    },
    "currentUser": {
      "defaultValue": "your_username"
    }
  }
}
```

### Service Configuration

Update the Oracle HCM service configuration in `webApps/jobRequisitions/services/OracleHCMService.json`:

```json
{
  "baseURL": "{{$application.variables.oracleHCMBaseURL}}",
  "authentication": {
    "type": "bearer",
    "token": "{{$application.variables.authToken}}"
  }
}
```

## Setup Instructions

### Prerequisites

1. Oracle Visual Builder Cloud Service instance
2. Oracle HCM Cloud instance with Recruiting module
3. Valid Oracle HCM Cloud credentials or OAuth2 tokens
4. Appropriate permissions for job requisition management

### Deployment Steps

1. **Import to VBCS**:
   - Log into your Oracle Visual Builder Cloud Service
   - Create a new application or import this project
   - Upload the project files maintaining the directory structure

2. **Configure API Connection**:
   - Update the `oracleHCMBaseURL` variable with your HCM instance URL
   - Configure authentication credentials
   - Test the service connection

3. **Deploy Application**:
   - Use VBCS deployment tools to publish the application
   - Configure any required security policies
   - Test all CRUD operations

### Local Development

For local development with VBCS:

1. Clone this repository
2. Import into Oracle Visual Builder Studio or VBCS
3. Configure the service endpoints
4. Run in development mode
5. Test with your Oracle HCM instance

## Usage

### Main Features

1. **Dashboard**: Landing page with navigation to job requisitions
2. **Job Requisitions List**: 
   - View all requisitions in a paginated table
   - Search by job title
   - Filter by status, type, etc.
   - Create new requisitions
   - Edit existing requisitions
   - Delete requisitions

3. **Create/Edit Form**:
   - Form validation
   - Required field indicators
   - Dropdown selections for standard values
   - Rich text descriptions
   - Save/cancel operations

### Navigation Flow

```
Main Start Page → Requisitions List → Create/Edit Form
     ↑                ↑                      ↓
     └────────────────┴──────────────────────┘
```

## Data Model

### JobRequisition Type

Key fields supported:
- `RequisitionId`: Unique identifier
- `RequisitionNumber`: Human-readable number
- `Title`: Job title
- `RecruitingType`: Type of recruiting
- `NumberOfOpenings`: Number of positions
- `StateName`: Current state
- `PhaseName`: Current phase
- `BusinessJustification`: Reason for position
- `MinimumSalary`/`MaximumSalary`: Compensation range
- `InternalDescription`/`ExternalDescription`: Job descriptions

## Error Handling

The application includes comprehensive error handling:

- **API Errors**: Network and HTTP error handling
- **Validation Errors**: Form validation with user feedback
- **Authentication Errors**: Token expiration and permission issues
- **Loading States**: Progress indicators during operations

## Security Considerations

- **Authentication**: Secure token-based authentication
- **Authorization**: Role-based access control through Oracle HCM
- **Data Validation**: Client and server-side validation
- **HTTPS**: Secure communication with Oracle Cloud

## Troubleshooting

### Common Issues

1. **Authentication Failures**:
   - Verify token validity and permissions
   - Check Oracle HCM instance URL
   - Ensure proper CORS configuration

2. **API Connection Issues**:
   - Verify network connectivity
   - Check service endpoint configuration
   - Review Oracle HCM API documentation

3. **Data Loading Problems**:
   - Check API response format
   - Verify data type mappings
   - Review browser console for errors

### Debug Mode

Enable debug logging in VBCS:
1. Open browser developer tools
2. Check console for error messages
3. Review network tab for API calls
4. Use VBCS debugging features

## Support

For issues and questions:
- Oracle Visual Builder documentation
- Oracle HCM Cloud API documentation
- Oracle Support portal

## License

This application is provided as a sample implementation. Please review Oracle licensing terms for production use.
