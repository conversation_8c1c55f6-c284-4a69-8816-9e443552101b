{"name": "oracle-hcm-job-requisitions-vbcs", "version": "1.0.0", "description": "Oracle HCM Job Requisitions CRUD Application built with VBCS", "main": "application.json", "scripts": {"validate": "node scripts/validate-config.js", "build": "echo 'Building VBCS application...' && npm run validate", "deploy": "node deployment/deploy-to-vbcs.js", "deploy:dev": "cross-env NODE_ENV=development npm run deploy", "deploy:staging": "cross-env NODE_ENV=staging npm run deploy", "deploy:prod": "cross-env NODE_ENV=production npm run deploy", "package": "node deployment/create-package.js", "test": "echo 'Run tests for VBCS application'", "start": "echo 'Starting VBCS development server...'", "clean": "rimraf deployment/package deployment/*.log"}, "keywords": ["oracle", "hcm", "vbcs", "job-requisitions", "recruiting", "crud"], "author": "Oracle VBCS Developer", "license": "Oracle", "repository": {"type": "git", "url": "https://github.com/your-org/oracle-hcm-job-requisitions-vbcs.git"}, "dependencies": {"@oracle/oraclejet": "^14.0.0"}, "devDependencies": {"eslint": "^8.0.0", "prettier": "^2.0.0"}, "engines": {"node": ">=14.0.0"}, "vbcs": {"version": "21.10.0", "jetVersion": "14.0.0", "features": ["rest-services", "data-binding", "validation", "navigation", "responsive-design"]}}